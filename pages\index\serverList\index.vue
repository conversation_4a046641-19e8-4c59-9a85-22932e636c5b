<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view class="server-list-item mt-16 p-16">
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-80 h-80 mr-20" />
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">宝塔Linux面板</text>
								<text class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24">在线2天</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：*************</text>
						</view>
					</view>
					<view
						class="flex items-center justify-between rd-16 px-8 py-4"
						style="border: 3rpx solid rgba(0, 0, 0, 0.1)"
					>
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-50 h-50 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20 text-#A7A7A7">88kb/s</text>
							<text class="text-20 mt-10 text-#A7A7A7">10kb/s</text>
						</view>
					</view>
				</view>
				<!-- 服务器监控指标网格 -->
				<view class="metrics-grid">
					<!-- 负载指标 - 第1列，跨2行 -->
					<view class="load-card">
						<view class="flex items-center justify-between w-full">
							<text class="load-title">负载</text>
							<text class="load-status px-8 py-4 rd-4 text-24">流畅</text>
						</view>
						<view class="load-chart-container">
							<view class="load-chart-fill">
								<text class="load-percentage">70%</text>
							</view>
						</view>
					</view>

					<!-- CPU指标 - 第2列第1行 -->
					<view class="metric-item cpu-item">
						<view class="flex flex-col items-center justify-between">
							<text class="metric-title">CPU</text>
							<text class="metric-subtitle px-8 py-4 rd-4 text-24">4核</text>
						</view>
						<ECharts
							canvas-id="cpu-chart"
							chart-type="gauge"
							:chart-data="getCpuChartData({ usage: 40, cores: 4 })"
							:height="110"
						/>
					</view>

					<!-- 内存指标 - 第3列第1行 -->
					<view class="metric-item memory-item">
						<view class="flex flex-col items-center justify-between">
							<text class="metric-title">内存</text>
							<text class="metric-subtitle px-8 py-4 rd-4 text-24">8GB</text>
						</view>
						<ECharts
							canvas-id="memory-chart"
							chart-type="gauge"
							:chart-data="getMemChartData({ usage: 60, total: '8GB' })"
							:height="110"
						/>
					</view>

					<!-- 磁盘指标 - 第2-3列第2行 -->
					<view class="disk-item">
						<view class="disk-header mb-20">
							<text class="disk-title">磁盘(/)</text>
							<text class="disk-total px-8 py-4 rd-4 text-24">40GB</text>
						</view>
						<view class="disk-progress-container">
							<view class="disk-progress-bar">
								<view class="disk-progress-fill"></view>
							</view>
							<text class="disk-percentage">48%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import { pageContainer, getCpuChartData, getMemChartData } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(2, 1fr);
		gap: 8rpx;
		margin-top: 32rpx;
		height: 300rpx; /* 设置固定高度确保2行布局 */
	}

	/* 负载卡片 - 第1列，跨2行 */
	.load-card {
		grid-column: 1;
		grid-row: 1 / 3;
		background: rgba(247, 247, 247, 1);
		border-radius: 24rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.load-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		align-self: flex-start;
	}

	.load-status {
		font-size: 24rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
		margin-bottom: 24rpx;
		align-self: flex-start;
	}

	/* 柱形图容器 */
	.load-chart-container {
		flex: 1;
		width: 80rpx;
		background: #fff;
		border-radius: 16rpx;
		position: relative;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		overflow: hidden;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
	}

	/* 柱形图填充 */
	.load-chart-fill {
		width: 100%;
		height: 70%;
		background: linear-gradient(180deg, #f9d71c 0%, #20a50a 100%);
		border-radius: 0 0 16rpx 16rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.load-percentage {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
	}

	.metric-item {
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* CPU指标 - 第2列第1行 */
	.cpu-item {
		grid-column: 2;
		grid-row: 1;
	}

	/* 内存指标 - 第3列第1行 */
	.memory-item {
		grid-column: 3;
		grid-row: 1;
	}

	.metric-title {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 4rpx;
	}

	.metric-subtitle {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	/* 磁盘指标 - 第2-3列第2行 */
	.disk-item {
		grid-column: 2 / 4;
		grid-row: 2;
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 24rpx;
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.disk-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.disk-title {
		font-size: 24rpx;
		color: #333;
	}

	.disk-total {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.disk-progress-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.disk-progress-bar {
		flex: 1;
		height: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
	}

	.disk-progress-fill {
		width: 48%;
		height: 100%;
		background: linear-gradient(90deg, #20a50a 0%, #4cd964 100%);
		border-radius: 8rpx;
		transition: width 0.3s ease;
	}

	.disk-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
	}
</style>
